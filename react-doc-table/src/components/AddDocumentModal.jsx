import React, { useState, useEffect } from 'react';
import { Modal, Button, Form, Input, Select, Upload, Switch, message } from 'antd';
import { UploadOutlined, InfoCircleOutlined } from '@ant-design/icons';
import { isValidCron } from 'cron-validator';
import { dict } from './const';
import styles from './AddDocumentModal.module.css';
import bookApi from '../api/BookApi';
const { Option, OptGroup } = Select;
const { Dragger } = Upload;

// 验证Cron表达式的函数
const validateCronExpression = (rule, value) => {
    if (!value) {
        return Promise.resolve();
    }

    // 使用cron-validator库验证Cron表达式
    const isOk = isValidCron(value, {
        // 启用秒字段支持
        seconds: true,
        // 启用月份和工作日的别名支持
        alias: true,
        // 允许使用?符号将天或工作日标记为空白
        allowBlankDay: true,
        // 支持数字7作为星期日
        allowSevenAsSunday: true,
    });

    if (!isOk) {
        return Promise.reject(new Error('Cron表达式格式错误，请检查语法'));
    }
    return Promise.resolve();
};

const AddDocumentModal = ({ visible, onCancel, onSuccess, loading, models = [], knowledgeBaseId }) => {
    const [form] = Form.useForm();
    const [step, setStep] = useState(1);
    const [documentType, setDocumentType] = useState('');
    const [files, setFiles] = useState([]);
    const [defaultValues, setDefaultValues] = useState(null);

    // 初始化获取知识库默认值
    useEffect(() => {
        if (knowledgeBaseId && visible) {
            const fetchDefaultValues = async () => {
                try {
                    const response = await bookApi.getBook(knowledgeBaseId);
                    if (response.code === 200) {
                        const kbData = response.data;
                        const embeddingRule = JSON.parse(kbData.embeddingRule || '{}');

                        const values = {
                            parseId: kbData.parseId || 1,
                            delimiter: embeddingRule.delimiter || ['。', '！'],
                            chunkTokenNum: embeddingRule.chunkTokenNum || 600,
                            embeddingModelId: kbData.embeddingModelId,
                            cronOpen: false,
                            cronExpression: '',
                        };

                        setDefaultValues(values);
                    }
                } catch (error) {
                    // 如果获取失败，使用硬编码默认值
                    const fallbackValues = {
                        parseId: 1,
                        delimiter: ['。', '！'],
                        chunkTokenNum: 600,
                        cronOpen: false,
                        cronExpression: '',
                    };
                    setDefaultValues(fallbackValues);
                }
            };

            fetchDefaultValues();
        }
    }, [knowledgeBaseId, visible]);

    // 重置状态
    const resetModal = () => {
        setStep(1);
        setDocumentType('');
        setFiles([]);
        form.resetFields();
    };

    // 关闭弹窗
    const handleCancel = () => {
        resetModal();
        onCancel();
    };

    // 选择文档类型
    const handleTypeSelect = (type) => {
        setDocumentType(type);
        setStep(2);

        // 使用缓存的默认值
        if (defaultValues) {
            form.setFieldsValue(defaultValues);
        }
    };

    // 返回第一步
    const handleBack = () => {
        setStep(1);
        form.resetFields();
    };

    // 文件上传处理
    const handleUpload = async ({ file, onSuccess, onError, onProgress }) => {
        try {
            // 创建FormData
            const formData = new FormData();
            formData.append('files', file);
            formData.append('knowledgeBaseId', knowledgeBaseId);

            // 调用上传API
            const response = await fetch('/rag/api/document/file/upload', {
                method: 'POST',
                body: formData,
                credentials: 'include',
            });

            const result = await response.json();

            if (result.code === 200 && result.data && result.data.length > 0) {
                // 上传成功，保存bosUrl到文件对象
                const uploadedFile = result.data[0];
                file.bosUrl = uploadedFile.bosUrl;
                file.uploadResponse = uploadedFile;

                onSuccess(result);
            } else {
                throw new Error(result.message || '文件上传失败');
            }
        } catch (error) {
            message.error(`文件 "${file.name}" 上传失败: ${error.message}`);
            onError(error);
        }
    };

    const handleBeforeUpload = (file) => {
        const isValidType = [
            'application/pdf',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'text/plain',
            'application/json',
            'application/vnd.ms-excel',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        ].includes(file.type);

        if (!isValidType) {
            message.error(
                `不支持的文件格式: ${file.type}。只支持 .doc | .txt | .docx | .pdf | .json | .excel 格式的文件`,
            );
            return Upload.LIST_IGNORE; // 使用 Upload.LIST_IGNORE 来阻止文件被添加到列表
        }

        const isLt50M = file.size / 1024 / 1024 < 50;
        if (!isLt50M) {
            message.error('文件大小不能超过50MB');
            return Upload.LIST_IGNORE; // 使用 Upload.LIST_IGNORE 来阻止文件被添加到列表
        }

        return true;
    };

    const handleFileChange = ({ fileList }) => {
        setFiles(fileList);
    };

    const handleRemove = (file) => {
        setFiles(files.filter((f) => f.uid !== file.uid));
    };

    // 表单提交
    const handleSubmit = async () => {
        try {
            // 使用 Ant Design 的表单验证
            const values = await form.validateFields();

            // 获取当前用户
            const getCurrentUser = () => {
                return sessionStorage.getItem('username') || 'current_user';
            };

            // 根据接口文档构建请求数据
            const body = {
                knowledgeBaseId: String(knowledgeBaseId), // 转换为字符串
                owner: getCurrentUser(),
                embeddingRule: {
                    delimiter: values.delimiter || ['。'], // 使用表单值，默认为中文句号
                    chunkTokenNum: values.chunkTokenNum || 600, // 使用表单值，默认为600
                },
                embeddingModelId: values.embeddingModelId || 3, // 使用表单值，默认为模型ID 3
                documents: [],
            };

            // 根据文档类型构建 documents 数组
            switch (documentType) {
                case 'upload':
                    // 文件验证已经在表单字段中处理，这里不需要重复验证
                    if (files.length === 0) {
                        return; // 直接返回，让表单验证处理
                    }

                    // 检查所有文件是否都已上传完成
                    const unuploadedFiles = files.filter((file) => !file.bosUrl && !file.uploadResponse?.bosUrl);
                    if (unuploadedFiles.length > 0) {
                        message.error('请等待所有文件上传完成后再提交');
                        return;
                    }

                    // 文件上传类型的文档数据
                    body.documents = files.map((file) => {
                        // 根据文件扩展名确定类型
                        const getFileType = (fileName) => {
                            const ext = fileName.split('.').pop()?.toLowerCase();
                            switch (ext) {
                                case 'pdf':
                                    return 'pdf';
                                case 'xlsx':
                                    return 'xlsx';
                                case 'xls':
                                    return 'xls';
                                case 'docx':
                                    return 'docx';
                                case 'doc':
                                    return 'doc';
                                case 'txt':
                                    return 'txt';
                                case 'json':
                                    return 'json';
                                case 'excel':
                                    return 'excel';
                                default:
                                    return 'pdf';
                            }
                        };

                        return {
                            title: file.name,
                            type: getFileType(file.name),
                            bosUrl: file.bosUrl || file.uploadResponse?.bosUrl || '', // 从上传响应中获取URL
                            knowledgeBaseId: String(knowledgeBaseId),
                            cronOpen: values.cronOpen || 0,
                            cronExpression: values.cronExpression || '', // 添加这一行
                        };
                    });
                    break;

                case 'link':
                    body.documents = [
                        {
                            title: values.title,
                            type: 'link',
                            sourceUrl: values.sourceUrl,
                            knowledgeBaseId: String(knowledgeBaseId),
                            cronOpen: values.cronOpen || 0,
                            cronExpression: values.cronExpression || '', // 添加这一行
                        },
                    ];
                    break;

                case 'virtual':
                    body.documents = [
                        {
                            title: values.title,
                            type: 'virtual',
                            knowledgeBaseId: String(knowledgeBaseId),
                            cronOpen: values.cronOpen || 0,
                            cronExpression: values.cronExpression || '', // 添加这一行
                        },
                    ];
                    break;
            }

            // 调用真实的 API
            try {
                const response = await fetch('/rag/api/document/add', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(body),
                });

                const result = await response.json();

                if (result.code === 200) {
                    message.success('文档添加成功');

                    // 调用成功回调刷新列表并选中新创建的文档
                    if (onSuccess) {
                        // 获取新创建的文档ID（通常在响应数据中）
                        const newDocuments = result.data || [];
                        const firstNewDocId = newDocuments.length > 0 ? newDocuments[0].id : null;

                        await onSuccess(firstNewDocId);
                    }

                    resetModal();
                    onCancel();
                } else {
                    message.error(result.message || '文档添加失败');
                }
            } catch (error) {
                message.error('网络错误，请重试');
            }
        } catch (error) {
            message.error('请检查表单输入');
        }
    };

    // 获取表单验证规则
    const getValidationRules = () => {
        const baseRules = {
            // 公共字段的验证规则
            embeddingModelId: [{ required: true, message: '请选择切词模型', trigger: 'change' }],

            // 非虚拟文档需要的字段
            // parseId: documentType !== 'virtual' ? [{ required: true, message: '请选择切词方式', trigger: 'change' }] : [],
            chunkTokenNum:
                documentType !== 'virtual'
                    ? [
                          { required: true, message: '请输入最大切片长度', trigger: 'blur' },
                          { pattern: /^\d+$/, message: '请输入有效的数字', trigger: 'blur' },
                      ]
                    : [],

            // 切片分隔符对于非虚拟文档是必填的
            delimiter:
                documentType !== 'virtual' ? [{ required: true, message: '请指定切词分隔符', trigger: 'change' }] : [],

            // Cron表达式验证
            cronExpression: [{ validator: validateCronExpression, trigger: 'blur' }],

            // 默认为空的规则，避免undefined
            title: [],
            sourceUrl: [],
        };

        // 根据文档类型添加特定的验证规则
        if (documentType === 'link') {
            baseRules.title = [{ required: true, message: '请输入文档标题', trigger: 'blur' }];
            baseRules.sourceUrl = [{ required: true, message: '请输入文档链接', trigger: 'blur' }];
        }

        if (documentType === 'virtual') {
            baseRules.title = [{ required: true, message: '请输入文档标题', trigger: 'blur' }];
        }

        return baseRules;
    };

    // 动态获取验证规则，确保 documentType 变化时规则也会更新
    const rules = React.useMemo(() => getValidationRules(), [documentType]);

    return (
        <Modal
            title={step === 1 ? '创建知识库' : '添加文档'}
            open={visible}
            onCancel={handleCancel}
            width={800}
            zIndex={10000}
            getContainer={false}
            maskClosable={false}
            footer={
                step === 1 ? (
                    <Button onClick={handleCancel}>取消</Button>
                ) : (
                    <div>
                        <Button onClick={handleBack}>返回</Button>
                        <Button onClick={handleCancel} style={{ marginLeft: 8 }}>
                            取消
                        </Button>
                        <Button type="primary" onClick={handleSubmit} loading={loading} style={{ marginLeft: 8 }}>
                            确定
                        </Button>
                    </div>
                )
            }
        >
            {step === 1 ? (
                // 第一步：选择创建类型
                <div>
                    <p style={{ marginBottom: 24, fontSize: 16 }}>请选择创建类型：</p>
                    <div style={{ display: 'flex', flexDirection: 'column', gap: 16 }}>
                        <div onClick={() => handleTypeSelect('upload')} className={styles.typeSelectorItem}>
                            <div style={{ fontSize: 16, fontWeight: 500, marginBottom: 8 }}>📁 文件上传</div>
                            <div style={{ color: '#666', fontSize: 14 }}>上传PDF、Word、Excel等文档</div>
                        </div>

                        <div onClick={() => handleTypeSelect('link')} className={styles.typeSelectorItem}>
                            <div style={{ fontSize: 16, fontWeight: 500, marginBottom: 8 }}>🗄️ 知识库</div>
                            <div style={{ color: '#666', fontSize: 14 }}>创建结构化知识库</div>
                        </div>

                        <div onClick={() => handleTypeSelect('virtual')} className={styles.typeSelectorItem}>
                            <div style={{ fontSize: 16, fontWeight: 500, marginBottom: 8 }}>✏️ 虚拟文档</div>
                            <div style={{ color: '#666', fontSize: 14 }}>手动创建虚拟文档</div>
                        </div>
                    </div>
                </div>
            ) : (
                // 第二步：具体表单
                <Form form={form} layout="vertical">
                    {/* 文件上传表单 */}
                    {documentType === 'upload' && (
                        <>
                            {/* 隐藏的文件验证字段 */}
                            <Form.Item
                                name="files"
                                rules={[
                                    {
                                        validator: () => {
                                            if (!files || files.length === 0) {
                                                return Promise.reject(new Error('请选择需要导入的文件'));
                                            }
                                            return Promise.resolve();
                                        },
                                    },
                                ]}
                                style={{ display: 'none' }}
                            >
                                <Input />
                            </Form.Item>

                            <Form.Item label="选择文件" required>
                                <Dragger
                                    multiple
                                    fileList={files}
                                    onChange={(info) => {
                                        handleFileChange(info);
                                        // 触发隐藏字段的验证
                                        form.setFieldsValue({ files: info.fileList.length > 0 ? 'hasFiles' : '' });
                                    }}
                                    onRemove={handleRemove}
                                    beforeUpload={handleBeforeUpload}
                                    customRequest={handleUpload}
                                    style={{ marginBottom: 16 }}
                                >
                                    <p className="ant-upload-drag-icon">
                                        <UploadOutlined style={{ fontSize: 48, color: '#999' }} />
                                    </p>
                                    <p className="ant-upload-text">
                                        将文档拖动至此处，或 <span style={{ color: '#1890ff' }}>点击上传</span>
                                    </p>
                                    <p className="ant-upload-hint" style={{ color: '#e6a23c' }}>
                                        单次上传文档数量为5个；单个文件小于50M；支持.doc | .txt | .docx | .pdf | .json |
                                        .excel
                                    </p>
                                </Dragger>
                            </Form.Item>
                        </>
                    )}

                    {/* 知识库表单 */}
                    {documentType === 'link' && (
                        <>
                            <Form.Item label="文档标题" name="title" rules={rules.title} required>
                                <Input placeholder="请输入文档标题" />
                            </Form.Item>
                            <Form.Item label="URL 链接" name="sourceUrl" rules={rules.sourceUrl} required>
                                <Input placeholder="文档的在线访问地址 URL" />
                            </Form.Item>
                        </>
                    )}

                    {/* 虚拟文档表单 */}
                    {documentType === 'virtual' && (
                        <Form.Item label="文档标题" name="title" rules={rules.title} required>
                            <Input placeholder="请输入文档标题" />
                        </Form.Item>
                    )}

                    {/* 公共表单项 */}
                    <Form.Item label="切词模型" name="embeddingModelId" required>
                        <Select placeholder="请选择切词模型">
                            {models.map((model) => (
                                <Option key={model.id} value={model.id}>
                                    {model.platform}-{model.name}
                                </Option>
                            ))}
                        </Select>
                    </Form.Item>

                    {documentType !== 'virtual' && (
                        <>
                            <Form.Item label="切词类型" name="parseId" required={documentType !== 'virtual'}>
                                <Select placeholder="请选择切词类型">
                                    {dict.parserOptions.map((option) => (
                                        <Option key={option.value} value={option.value}>
                                            {option.label}
                                        </Option>
                                    ))}
                                </Select>
                            </Form.Item>

                            <Form.Item
                                label={<>切片分隔符 &nbsp;&nbsp;</>}
                                tooltip="按照指定的标识符切分文本,可以有多个分割符"
                                name="delimiter"
                                required={documentType !== 'virtual'}
                            >
                                <Select mode="multiple" placeholder="请选择切词分隔符，可以多个">
                                    {dict.delimiters.map((group) => (
                                        <OptGroup key={group.label} label={group.label}>
                                            {group.options.map((option) => (
                                                <Option key={option.value} value={option.value}>
                                                    {option.label}
                                                </Option>
                                            ))}
                                        </OptGroup>
                                    ))}
                                </Select>
                            </Form.Item>

                            <Form.Item
                                label={<>最大切片长 &nbsp;&nbsp;</>}
                                tooltip="切片长度越大，召回的上下文越丰富。长度越小，召回的信息越精简"
                                name="chunkTokenNum"
                                rules={
                                    documentType !== 'virtual'
                                        ? [
                                              { required: true, message: '请输入最大切片长度' },
                                              { pattern: /^\d+$/, message: '请输入有效的数字' },
                                          ]
                                        : []
                                }
                                required={documentType !== 'virtual'}
                            >
                                <Input placeholder="最大切片长度" type="number" />
                            </Form.Item>
                        </>
                    )}

                    {documentType === 'link' && (
                        <>
                            <Form.Item label="定时任务：" name="cronOpen" valuePropName="checked">
                                <Switch />
                            </Form.Item>

                            <Form.Item
                                noStyle
                                shouldUpdate={(prevValues, currentValues) =>
                                    prevValues.cronOpen !== currentValues.cronOpen
                                }
                            >
                                {({ getFieldValue }) => {
                                    return getFieldValue('cronOpen') ? (
                                        <Form.Item
                                            label={<span>Cron 表达式：</span>}
                                            tooltip="请输入Cron表达式，例如：0 0 12 * * ?"
                                            name="cronExpression"
                                            rules={rules.cronExpression}
                                        >
                                            <Input placeholder="请输入Cron表达式，例如：0 0 12 * * ?" />
                                        </Form.Item>
                                    ) : null;
                                }}
                            </Form.Item>
                        </>
                    )}
                </Form>
            )}
        </Modal>
    );
};

export default AddDocumentModal;
